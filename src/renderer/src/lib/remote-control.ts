import { PocketBaseService } from './pocketbase'

export interface RemoteCommand {
  id: string
  target_device: string
  command: 'Screenshot' | 'Shutdown' | 'Reboot' | 'Sleep' | 'Lock' | 'Unlock'
  status: 'pending' | 'executed' | 'failed'
  output?: string
  created_by: string
  executed_by?: string
  created: string
  updated: string
}

export class RemoteControlService {
  private static deviceId: string
  private static unsubscribe: (() => void) | null = null

  static initialize(deviceId: string) {
    this.deviceId = deviceId
    this.startListening()
  }

  static cleanup() {
    if (this.unsubscribe) {
      this.unsubscribe()
      this.unsubscribe = null
    }
  }

  private static startListening() {
    // Subscribe to remote commands for this device
    this.unsubscribe = PocketBaseService.pb.collection('remote_commands').subscribe('*', (e) => {
      if (e.record.target_device === this.deviceId && e.record.status === 'pending') {
        this.executeCommand(e.record as RemoteCommand)
      }
    })
  }

  private static async executeCommand(command: RemoteCommand) {
    console.log(`Executing command: ${command.command}`)
    
    try {
      let result = ''
      
      switch (command.command) {
        case 'Screenshot':
          result = await this.takeScreenshot()
          break
        case 'Shutdown':
          result = await this.shutdown()
          break
        case 'Reboot':
          result = await this.reboot()
          break
        case 'Sleep':
          result = await this.sleep()
          break
        case 'Lock':
          result = await this.lockScreen()
          break
        case 'Unlock':
          result = await this.unlockScreen()
          break
        default:
          throw new Error(`Unknown command: ${command.command}`)
      }

      // Update command status to executed
      await PocketBaseService.pb.collection('remote_commands').update(command.id, {
        status: 'executed',
        output: result,
        executed_by: new Date().toISOString()
      })

    } catch (error) {
      console.error('Failed to execute command:', error)
      
      // Update command status to failed
      await PocketBaseService.pb.collection('remote_commands').update(command.id, {
        status: 'failed',
        output: error instanceof Error ? error.message : 'Unknown error',
        executed_by: new Date().toISOString()
      })
    }
  }

  private static async takeScreenshot(): Promise<string> {
    const platform = window.electron?.process?.platform || 'unknown'
    const timestamp = Date.now()
    const filename = `screenshot_${this.deviceId}_${timestamp}.png`
    
    let command = ''
    
    switch (platform) {
      case 'linux':
        // Try multiple screenshot tools
        command = `scrot "${filename}" || gnome-screenshot -f "${filename}" || import -window root "${filename}"`
        break
        
      case 'win32':
        // PowerShell method for Windows
        command = `powershell -command "Add-Type -AssemblyName System.Windows.Forms,System.Drawing; $bounds = [System.Windows.Forms.Screen]::PrimaryScreen.Bounds; $bmp = New-Object System.Drawing.Bitmap $bounds.width, $bounds.height; $graphics = [System.Drawing.Graphics]::FromImage($bmp); $graphics.CopyFromScreen($bounds.X, $bounds.Y, 0, 0, $bounds.size); $bmp.Save('${filename}'); $bmp.Dispose(); $graphics.Dispose()"`
        break
        
      case 'darwin':
        // macOS screencapture
        command = `screencapture -x "${filename}"`
        break
        
      default:
        throw new Error(`Unsupported platform: ${platform}`)
    }

    try {
      // Execute screenshot command
      const result = await window.electron.ipcRenderer.invoke('execute-command', command)
      
      // Upload screenshot to PocketBase
      const file = await this.readFileAsBlob(filename)
      const formData = new FormData()
      formData.append('device', this.deviceId)
      formData.append('image', file, filename)
      
      await PocketBaseService.pb.collection('screenshots').create(formData)
      
      // Clean up local file
      await window.electron.ipcRenderer.invoke('delete-file', filename)
      
      return `Screenshot taken and uploaded: ${filename}`
    } catch (error) {
      throw new Error(`Screenshot failed: ${error}`)
    }
  }

  private static async shutdown(): Promise<string> {
    const platform = window.electron?.process?.platform || 'unknown'
    let command = ''
    
    switch (platform) {
      case 'linux':
        command = 'sudo shutdown -h now || systemctl poweroff'
        break
      case 'win32':
        command = 'shutdown /s /t 0'
        break
      case 'darwin':
        command = 'sudo shutdown -h now'
        break
      default:
        throw new Error(`Unsupported platform: ${platform}`)
    }

    await window.electron.ipcRenderer.invoke('execute-command', command)
    return 'Shutdown command executed'
  }

  private static async reboot(): Promise<string> {
    const platform = window.electron?.process?.platform || 'unknown'
    let command = ''
    
    switch (platform) {
      case 'linux':
        command = 'sudo reboot || systemctl reboot'
        break
      case 'win32':
        command = 'shutdown /r /t 0'
        break
      case 'darwin':
        command = 'sudo shutdown -r now'
        break
      default:
        throw new Error(`Unsupported platform: ${platform}`)
    }

    await window.electron.ipcRenderer.invoke('execute-command', command)
    return 'Reboot command executed'
  }

  private static async sleep(): Promise<string> {
    const platform = window.electron?.process?.platform || 'unknown'
    let command = ''
    
    switch (platform) {
      case 'linux':
        command = 'systemctl suspend'
        break
      case 'win32':
        command = 'rundll32.exe powrprof.dll,SetSuspendState 0,1,0'
        break
      case 'darwin':
        command = 'pmset sleepnow'
        break
      default:
        throw new Error(`Unsupported platform: ${platform}`)
    }

    await window.electron.ipcRenderer.invoke('execute-command', command)
    return 'Sleep command executed'
  }

  private static async lockScreen(): Promise<string> {
    const platform = window.electron?.process?.platform || 'unknown'
    let command = ''
    
    switch (platform) {
      case 'linux':
        command = 'gnome-screensaver-command -l || qdbus org.kde.screensaver /ScreenSaver Lock || i3lock || betterlockscreen -l'
        break
      case 'win32':
        command = 'rundll32.exe user32.dll,LockWorkStation'
        break
      case 'darwin':
        command = '/System/Library/CoreServices/Menu\\ Extras/User.menu/Contents/Resources/CGSession -suspend'
        break
      default:
        throw new Error(`Unsupported platform: ${platform}`)
    }

    await window.electron.ipcRenderer.invoke('execute-command', command)
    return 'Lock screen command executed'
  }

  private static async unlockScreen(): Promise<string> {
    // Note: Unlocking is generally not possible for security reasons
    // This would typically require user interaction
    return 'Unlock screen not supported for security reasons'
  }

  private static async readFileAsBlob(filename: string): Promise<Blob> {
    const buffer = await window.electron.ipcRenderer.invoke('read-file', filename)
    return new Blob([buffer], { type: 'image/png' })
  }

  // Method to test if commands work on current platform
  static async testPlatformSupport(): Promise<{ [key: string]: boolean }> {
    const platform = window.electron?.process?.platform || 'unknown'
    
    const support = {
      screenshot: false,
      shutdown: false,
      reboot: false,
      sleep: false,
      lock: false
    }

    try {
      // Test screenshot capability
      if (platform === 'linux') {
        const result = await window.electron.ipcRenderer.invoke('execute-command', 'which scrot || which gnome-screenshot || which import')
        support.screenshot = !result.includes('not found')
      } else if (platform === 'win32') {
        support.screenshot = true // PowerShell is always available on Windows
      } else if (platform === 'darwin') {
        support.screenshot = true // screencapture is built-in on macOS
      }

      // Other commands are generally available on all platforms
      support.shutdown = true
      support.reboot = true
      support.sleep = true
      support.lock = true

    } catch (error) {
      console.error('Error testing platform support:', error)
    }

    return support
  }
}

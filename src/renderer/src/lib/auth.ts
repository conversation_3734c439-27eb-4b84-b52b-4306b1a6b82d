import { Client } from '@/types'

// Mock user database - in production this would connect to your backend
const mockUsers = [
  { username: 'user1', password: 'pass1', timePackage: 120 }, // 2 hours
  { username: 'user2', password: 'pass2', timePackage: 180 }, // 3 hours
  { username: 'admin', password: 'admin123', timePackage: 480, isAdmin: true }, // 8 hours
  { username: 'guest', password: 'guest', timePackage: 60 }, // 1 hour
]

export interface LoginCredentials {
  username: string
  password: string
}

export interface AuthResult {
  success: boolean
  client?: Client
  error?: string
}

export class AuthService {
  static async authenticate(credentials: LoginCredentials): Promise<AuthResult> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    const user = mockUsers.find(
      u => u.username === credentials.username && u.password === credentials.password
    )

    if (!user) {
      return {
        success: false,
        error: 'Invalid username or password'
      }
    }

    // Create client session
    const client: Client = {
      id: Date.now().toString(),
      username: user.username,
      timeRemaining: user.timePackage,
      sessionStart: new Date(),
      isActive: true,
      machineId: this.getMachineId()
    }

    return {
      success: true,
      client
    }
  }

  static getMachineId(): string {
    // In a real implementation, this would get the actual machine ID
    return `PC-${Math.floor(Math.random() * 100).toString().padStart(3, '0')}`
  }

  static validateSession(client: Client): boolean {
    // Check if session is still valid
    const now = new Date()
    const sessionDuration = Math.floor((now.getTime() - client.sessionStart.getTime()) / 60000) // minutes
    
    return client.isActive && client.timeRemaining > 0 && sessionDuration < client.timeRemaining
  }

  static extendSession(client: Client, additionalMinutes: number): Client {
    return {
      ...client,
      timeRemaining: client.timeRemaining + additionalMinutes
    }
  }

  static endSession(client: Client): void {
    // In a real implementation, this would save session data to backend
    console.log(`Session ended for ${client.username}`)
    console.log(`Session duration: ${Math.floor((new Date().getTime() - client.sessionStart.getTime()) / 60000)} minutes`)
  }
}

// Session storage utilities
export class SessionStorage {
  private static readonly SESSION_KEY = 'gaming_cafe_session'

  static saveSession(client: Client): void {
    try {
      localStorage.setItem(this.SESSION_KEY, JSON.stringify({
        ...client,
        sessionStart: client.sessionStart.toISOString()
      }))
    } catch (error) {
      console.error('Failed to save session:', error)
    }
  }

  static loadSession(): Client | null {
    try {
      const sessionData = localStorage.getItem(this.SESSION_KEY)
      if (!sessionData) return null

      const parsed = JSON.parse(sessionData)
      return {
        ...parsed,
        sessionStart: new Date(parsed.sessionStart)
      }
    } catch (error) {
      console.error('Failed to load session:', error)
      return null
    }
  }

  static clearSession(): void {
    try {
      localStorage.removeItem(this.SESSION_KEY)
    } catch (error) {
      console.error('Failed to clear session:', error)
    }
  }
}

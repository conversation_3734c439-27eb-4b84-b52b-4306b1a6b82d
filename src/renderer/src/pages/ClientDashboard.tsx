import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Client } from '@/types'
import SystemMonitor from '@/components/SystemMonitor'
import { NetworkMonitor } from '@/lib/network-monitor'

interface ClientDashboardProps {
  client: Client
  onLogout: () => void
  onMinimize: () => void
}

export default function ClientDashboard({ client, onLogout, onMinimize }: ClientDashboardProps) {
  const [timeRemaining, setTimeRemaining] = useState(client.timeRemaining)
  const [sessionDuration, setSessionDuration] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          // Time's up - auto logout
          onLogout()
          return 0
        }
        return prev - 1
      })
      
      setSessionDuration(prev => prev + 1)
    }, 60000) // Update every minute

    return () => clearInterval(timer)
  }, [onLogout])

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
  }

  const getTimeColor = () => {
    if (timeRemaining <= 15) return 'text-red-500'
    if (timeRemaining <= 30) return 'text-yellow-500'
    return 'text-green-500'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold text-white">
            Welcome, {client.username}!
          </h1>
          <div className="text-sm text-gray-300">
            Session started: {client.sessionStart.toLocaleTimeString()}
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          <Button 
            variant="outline" 
            onClick={onMinimize}
            className="bg-blue-600 hover:bg-blue-700 text-white border-blue-500"
          >
            Minimize to Tray
          </Button>
          <Button 
            variant="destructive" 
            onClick={onLogout}
          >
            End Session
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Time Remaining Card */}
        <Card className="gaming-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>⏰</span>
              <span>Time Remaining</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-4xl font-bold ${getTimeColor()}`}>
              {formatTime(timeRemaining)}
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              {timeRemaining <= 15 && "⚠️ Session ending soon!"}
            </p>
          </CardContent>
        </Card>

        {/* Session Info Card */}
        <Card className="gaming-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>📊</span>
              <span>Session Info</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Duration:</span>
                <span className="font-semibold">{formatTime(sessionDuration)}</span>
              </div>
              <div className="flex justify-between">
                <span>Machine:</span>
                <span className="font-semibold">{client.machineId || 'PC-001'}</span>
              </div>
              <div className="flex justify-between">
                <span>Status:</span>
                <span className="font-semibold text-green-500">Active</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions Card */}
        <Card className="gaming-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>🎮</span>
              <span>Quick Actions</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Button className="w-full" variant="outline">
                🌐 Open Browser
              </Button>
              <Button className="w-full" variant="outline">
                🎵 Media Player
              </Button>
              <Button className="w-full" variant="outline">
                📁 File Manager
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* System Status Card */}
        <Card className="gaming-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>💻</span>
              <span>System Status</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>CPU:</span>
                <span className="font-semibold text-green-500">Normal</span>
              </div>
              <div className="flex justify-between">
                <span>Memory:</span>
                <span className="font-semibold text-green-500">Good</span>
              </div>
              <div className="flex justify-between">
                <span>Network:</span>
                <span className="font-semibold text-green-500">Connected</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Rules & Info Card */}
        <Card className="gaming-card md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>📋</span>
              <span>Gaming Cafe Rules</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li>• No downloading or installing unauthorized software</li>
              <li>• Keep noise levels reasonable for other customers</li>
              <li>• No food or drinks near the computer</li>
              <li>• Report any technical issues to staff immediately</li>
              <li>• Save your work regularly - sessions end automatically</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

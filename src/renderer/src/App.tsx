import { useState, useEffect } from 'react'
import LoginPage from './pages/LoginPage'
import ClientDashboard from './pages/ClientDashboard'
import { Client, AppMode } from './types'
import { AuthService, SessionStorage } from './lib/auth'

function App(): React.JSX.Element {
  const [appMode, setAppMode] = useState<AppMode>('login')
  const [currentClient, setCurrentClient] = useState<Client | null>(null)

  // Handle login
  const handleLogin = (client: Client) => {
    setCurrentClient(client)
    setAppMode('client')

    // Send message to main process to minimize window
    window.electron.ipcRenderer.send('client-logged-in', client)
  }

  // Handle logout
  const handleLogout = () => {
    if (currentClient) {
      AuthService.endSession(currentClient)
      SessionStorage.clearSession()
    }

    setCurrentClient(null)
    setAppMode('login')

    // Send message to main process to show window again
    window.electron.ipcRenderer.send('client-logged-out')
  }

  // Handle minimize to tray
  const handleMinimize = () => {
    setAppMode('minimized')

    // Send message to main process to minimize to tray
    window.electron.ipcRenderer.send('minimize-to-tray')
  }

  // Listen for messages from main process
  useEffect(() => {
    const handleShowWindow = () => {
      if (currentClient) {
        setAppMode('client')
      } else {
        setAppMode('login')
      }
    }

    // Listen for show window events from system tray
    window.electron.ipcRenderer.on('show-window', handleShowWindow)

    return () => {
      window.electron.ipcRenderer.removeAllListeners('show-window')
    }
  }, [currentClient])

  // Render based on app mode
  switch (appMode) {
    case 'login':
      return <LoginPage onLogin={handleLogin} />

    case 'client':
      return currentClient ? (
        <ClientDashboard
          client={currentClient}
          onLogout={handleLogout}
          onMinimize={handleMinimize}
        />
      ) : (
        <LoginPage onLogin={handleLogin} />
      )

    case 'minimized':
      // When minimized, show a simple overlay or nothing
      return (
        <div className="min-h-screen bg-black flex items-center justify-center">
          <div className="text-white text-center">
            <h2 className="text-2xl mb-4">Session Active</h2>
            <p>Gaming session is running in the background</p>
            <p className="text-sm text-gray-400 mt-2">
              Check system tray to restore window
            </p>
          </div>
        </div>
      )

    default:
      return <LoginPage onLogin={handleLogin} />
  }
}

export default App

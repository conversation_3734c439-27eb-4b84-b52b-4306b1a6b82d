export interface Client {
  id: string
  username: string
  email?: string
  timeRemaining: number // in minutes
  sessionStart: Date
  isActive: boolean
  machineId?: string
}

export interface Session {
  id: string
  clientId: string
  startTime: Date
  endTime?: Date
  duration: number // in minutes
  cost: number
  status: 'active' | 'completed' | 'paused'
}

export interface AppState {
  isKioskMode: boolean
  currentClient: Client | null
  isLoggedIn: boolean
  isMinimized: boolean
  adminMode: boolean
}

export interface NetworkUsage {
  downloadSpeed: number
  uploadSpeed: number
  totalDownload: number
  totalUpload: number
  timestamp: Date
}

export interface SystemInfo {
  cpuUsage: number
  memoryUsage: number
  diskUsage: number
  networkUsage: NetworkUsage
}

export type AppMode = 'login' | 'client' | 'admin' | 'minimized'
